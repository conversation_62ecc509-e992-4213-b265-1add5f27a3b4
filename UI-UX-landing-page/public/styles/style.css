body {
  background: white;
  font-family: "DM Sans", sans-serif;
}

.intro {
  /* margin-top: 100px; */
  width: 100%;
  min-height: 400px;
  background: #222222;
  color: white;
  max-width: 93%;
  border-radius: 40px;
  padding-top: 150px;
}

/* Navbar Styles */
.navbar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.5rem 0;
  margin-bottom: 20px;
  position: relative;
}

.navbar-brand {
  position: absolute;
  left: 30px;
  display: flex;
  align-items: center;
}

img.nav-logo {
  width: 78px;
  height: auto;
}

/* Desktop Navigation Container - Centered */
.navbar-container {
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 45px;
  background: rgba(57, 57, 57, 0.8);
  padding: 8px 40px;
  backdrop-filter: blur(10px);
}

.nav-pills {
  margin: 0;
  gap: 0;
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
}

.nav-pills .nav-item {
  margin: 0;
}

.nav-pills .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  border: none;
  background: transparent;
  margin: 0 4px;
  text-decoration: none;
  display: block;
}

.nav-pills .nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-pills .nav-link.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
}

/* Hamburger Menu Styles */
.hamburger-menu {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
  z-index: 1001;
  position: absolute;
  right: 0;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: white;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

/* Hamburger Animation - Transform to X */
.hamburger-menu.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-menu.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger-menu.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation Dropdown */
.mobile-nav-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(57, 57, 57, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(15px);
  padding: 20px;
  min-width: 250px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-nav-dropdown.active {
  display: block;
  opacity: 1;
  visibility: visible;
}

.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-item {
  margin: 0;
}

.mobile-nav-link {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  text-decoration: none;
  border-radius: 15px;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.95rem;
}

.mobile-nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.logo {
  border-radius: 10px;
}

.hero-section {
  min-height: 85vh;
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding-bottom: 30px;
}

/* New Masterclass Badge */
.masterclass-badge-new {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: #7A86F2;
  color: #fff;
  padding: 10px 64px;
  border-radius: 50px;
  font-size:18px;
  font-weight: 400;
  letter-spacing: 0.5px;
  text-transform: uppercase;

}

.rocket-icon {
  font-size: 1rem;
}

h1 {
  font-size: 3.5rem;
  line-height: 1.2;
}

.highlight {
  color: #9DFF50;
  font-weight: bold;
}

.no-space {
  margin-left: -0.1rem;
}

.subtitle {
  font-size: 2.25rem;
  line-height: 2.5rem;
  color: #6B6B6B;
}

.description {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 60%;
  font-weight: 300;
  line-height: 1.6;
  margin: auto;
}

.event-item {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.9);
}



.btn {
  padding: 0.8rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-register {
  background-color: #9DFF50;
  color: #000;
  border: 1px solid #9DFF50;
}

.btn-register:hover {
  background-color: #9DFF50;
  color: #000;
  transform: translateY(-2px);
}

.btn-roadmap {
  background-color: transparent;
  color: #9DFF50;
  border: 1px solid #9DFF50;
  padding: 1rem 2.5rem;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-roadmap:hover {
  background-color: rgba(157, 255, 80, 0.1);
  color: #9DFF50;
  transform: translateY(-2px);
}

/* Final Register Button */
.btn-register-primary {
  background-color: #9DFF50;
  color: #000;
  border: none;
  padding: 1rem 2.5rem;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-register-primary:hover {
  background-color: #8AE63B;
  color: #000;
  transform: translateY(-2px);
}

.btn-register-final {
  background-color: #9DFF50;
  color: #000;
  border: none;
  padding: 1.2rem 3rem;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 15px rgba(157, 255, 80, 0.4);
}

.btn-register-final:hover {
  background-color: #8AE63B;
  color: #000;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(157, 255, 80, 0.5);
}



/* New Seats Warning */
.seats-warning-new {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #F8A8A8;
  font-size: 1.2rem;
  font-weight: 300;
  margin-top: 20px;
}

.warning-icon {
  font-size: 1.1rem;
}

/* Registration Modal */
.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: 1000;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 16px;
  max-width: 900px;
  width: 100%;
  max-height: 95vh;
  overflow-y: visible;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  padding: 30px;
  height: auto;
  min-height: 600px;
}

/* Larger modal for registration form */
.modal-container.form-step {
  max-width: 900px;
  max-height: 95vh;
  min-height: 750px;
  padding: 20px 40px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  z-index: 10;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #000;
}

.modal-header {
  /* padding: 30px 40px 30px; */
  padding-top: 30px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: x-large;
  font-weight: bold;
}

.modal-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

/* Progress Steps */
.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.step-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  background-color: #e8e8e8;
  color: #999;
  border: 3px solid #e8e8e8;
  transition: all 0.3s ease;
}

.step.active .step-icon {
  background-color: #9DFF50;
  color: #333;
  border-color: #9DFF50;
  box-shadow: 0 2px 8px rgba(157, 255, 80, 0.3);
}

.step.completed .step-icon {
  background-color: #9DFF50;
  color: #333;
  border-color: #9DFF50;
  box-shadow: 0 2px 8px rgba(157, 255, 80, 0.3);
}

/* Update icon styles */
.step-icon .clock-icon {
  font-size: 22px;
}

.step-icon .info-icon {
  font-size: 20px;
}

.step-icon .success-icon {
  font-size: 20px;
  font-weight: bold;
}

/* Hide original icons when step is completed and show checkmark */
.step.completed .step-icon .clock-icon,
.step.completed .step-icon .info-icon {
  display: none;
}

.step.completed .step-icon::after {
  content: '✓';
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.step-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.step.active .step-label {
  color: #9DFF50;
}

.step-line {
  width: 80px;
  height: 3px;
  background-color: #e8e8e8;
  margin: 0 15px;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.step.completed+.step-line {
  background-color: #9DFF50;
}

.step.active+.step-line {
  background-color: #e8e8e8;
}

/* Modal Content */
.modal-content {
  display: flex;
  flex-direction: row;
  gap: 20px;
  height: 500px;
  /* margin-top: 20px; */
  width: 100%;
}

/* Adjust modal content height for form step */
.modal-container.form-step .modal-content {
  height: auto;
  min-height: 400px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Calendar Section */
.calendar-section {
  flex: 0 0 350px;
  background: #1a1a2e;
  padding: 20px;
  color: white;
  display: flex;
  flex-direction: column;
  gap: 15px;
  border-radius: 12px;
  height: 500px;
  width: 50%;
}

.event-card {
  text-align: center;
  margin-bottom: 5px;
}

.event-logo {
  width: 60px;
  height: 60px;
  background-color: #9DFF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.ux-logo {
  font-size: 1.5rem;
  font-weight: 900;
  color: #000;
  background: #9DFF50;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.event-card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: white;
}

.event-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

/* Calendar Widget */
.calendar-widget {
  background-color: #ffffff1a;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  flex: 1;
  overflow: hidden;
  height: max-content;
}

.calendar-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.calendar-header {
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: white;
  flex: 1;
}

.nav-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background-color: rgba(157, 255, 80, 0.2);
  color: #9DFF50;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-btn:disabled:hover {
  background-color: transparent;
  color: #fff;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  margin-bottom: 5px;
}

.calendar-days span {
  text-align: center;
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  padding: 8px 4px;
}

.calendar-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.calendar-dates span {
  text-align: center;
  padding: 3px 4px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.calendar-dates span:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.calendar-dates span.selected {
  background-color: #9DFF50 !important;
  color: #000 !important;
  font-weight: 600;
  border: 2px solid #7ED321;
  box-shadow: 0 2px 8px rgba(157, 255, 80, 0.4);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.calendar-dates span.disabled {
  color: #666;
  background-color: transparent;
  cursor: not-allowed;
  opacity: 0.5;
}

.calendar-dates span.enabled {
  color: #fff;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.calendar-dates span.enabled:hover:not(.selected) {
  background-color: rgba(157, 255, 80, 0.2);
  color: #9DFF50;
}

.calendar-dates span.selected:hover {
  background-color: #9DFF50 !important;
  color: #000 !important;
  transform: scale(1.08);
}

.calendar-dates span.today {
  border: 2px solid #9DFF50;
  color: #9DFF50;
}

/* Time Section */
.time-section {
  flex: 1;
  padding: 20px;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  gap: 20px;
  border-radius: 12px;
  height: 500px;
  width: 50%;
}

.meeting-duration h4,
.time-selection h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.duration-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
}

.duration-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #9DFF50;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: #000;
}

.time-subtitle {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.timezone-selector {
  margin-bottom: 20px;
}

.timezone-selector select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #9DFF50;
  border-radius: 8px;
  font-size: 0.9rem;
  background-color: white;
  color: #333;
  outline: none;
}

.time-slots {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
  max-height: 250px;
}

.time-slot {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
}

.time-slot:hover {
  border-color: #9DFF50;
  background-color: rgba(157, 255, 80, 0.05);
}

.time-slot.active {
  border-color: #9DFF50;
  background-color: rgba(157, 255, 80, 0.1);
  font-weight: 500;
}

.time-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #9DFF50;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: #000;
}

/* Custom Scrollbar for time slots */
.time-slots::-webkit-scrollbar {
  width: 8px;
}

.time-slots::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.time-slots::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.time-slots::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.learn-section {
  background: white;
  color: #333;
  padding: 100px 0;
  position: relative;
}

.learn-header {
  margin-bottom: 60px;
}

.learn-title {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.2;
}

.learn-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.learn-content-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  max-width: 800px;
  margin: 0 auto;
}

.learn-star {
  position: absolute;
  top: -20px;
  left: -40px;
  z-index: 2;
}

.star-decoration {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.learn-content-box {
  background-color: #F1F5FD;
  border-radius: 32px;
  padding: 40px 80px;
  position: relative;
  width: 100%;
  max-width: 700px;
}

.learn-list {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #333;
}

.learn-list li {
  margin-bottom: 10px;
    position: relative;
    padding-left: 25px;
    font-size: 1.3rem;
    line-height: 1.6;
}

.learn-list li:last-child {
  margin-bottom: 0;
}

.learn-list li:before {
  content: "•";
  color: #333;
  font-weight: bold;
  position: absolute;
  left: 0;
  font-size: 1.2rem;
}

/* AI Design Tools Section */
.ai-tools-section {
  background-color: #2a2a2a;
  color: white;
  padding: 100px 0 0 0;
  position: relative;
  overflow: hidden;

}

.ai-tools-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom right, #F1F5FD 49%, #2a2a2a 51%);
  z-index: 1;
}


/* Decorative Stars */
.star-decoration {
  position: absolute;
  z-index: 1;
}

.star-icon-svg {
  width: 40px;
  height: 40px;
  opacity: 0.6;
}

.star-top-left {
  top: 80px;
  left: 10%;
}

.star-bottom-right {
  bottom: 80px;
  right: 10%;
}

.tools-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
  z-index: 2;
}

.tools-header h2 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.tools-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.tool-card {
  flex: 0 0 calc(33.333% - 20px);
  max-width: calc(33.333% - 20px);
}

/* First 6 cards - normal 3x2 grid */
.tool-card:nth-child(1),
.tool-card:nth-child(2),
.tool-card:nth-child(3),
.tool-card:nth-child(4),
.tool-card:nth-child(5),
.tool-card:nth-child(6) {
  flex: 0 0 calc(33.333% - 20px);
}

/* Last row - center the two cards */
.tool-card:nth-child(7) {
  flex: 0 0 calc(33.333% - 20px);
  margin-left: calc(16.666% + 10px);
}

.tool-card:nth-child(8) {
  flex: 0 0 calc(33.333% - 20px);
  /* margin-right: calc(16.666% + 10px); */
}

.tool-card {
  background: #2A2A2A;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 25px;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  transition: all 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-3px);
  background: #333333;
  border-color: rgba(255, 255, 255, 0.2);
}

.tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  flex-shrink: 0;
  object-fit: contain;
  border: 1px solid #ccc;
}

.tool-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: white;
}

.tool-info p {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

/* Tools Message */
.tools-message {
  margin-top: 60px;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.tools-message-content {
  background-color: #D0FFD0;
  color: #333;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.check-icon {
  background-color: #4CAF50;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .intro {
    margin: 15px;
    border-radius: 30px;
    padding: 30px 20px;
  }

  h1 {
    font-size: 3rem;
  }

  .subtitle {
    font-size: 1.6rem;
  }

  .learn-section {
    padding: 80px 0;
  }

  .learn-title {
    font-size: 2.5rem;
  }

  .learn-content-box {
    padding: 40px 50px;
  }

  .learn-star {
    left: -30px;
    top: -15px;
  }

  .star-decoration {
    width: 50px;
    height: 50px;
  }

  /* Navbar Tablet Responsive */
  .navbar-container {
    padding: 8px 30px;
  }

  .nav-pills .nav-link {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .tools-grid {
    gap: 25px;
    padding: 0 20px;
  }

  .tool-card {
    flex: 0 0 calc(50% - 12.5px);
    max-width: calc(50% - 12.5px);
  }

  .tool-card:nth-child(7) {
    flex: 0 0 100%;
    max-width: 100%;
    margin: 0 auto;
  }

  .tools-header h2 {
    font-size: 2.8rem;
  }

  .tools-message-content {
    padding: 12px 25px;
    font-size: 0.9rem;
  }

  .star-icon-svg {
    width: 35px;
    height: 35px;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {


  .intro {
    margin: 10px;
    border-radius: 20px;
    padding: 20px 15px;
    min-height: 500px;
  }

  h1 {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.5rem;
  }

  .description {
    font-size: 1rem;
    max-width: 100%;
  }

 



  .learn-section {
    padding: 60px 0;
  }

  .learn-title {
    font-size: 2rem;
  }

  .learn-subtitle {
    font-size: 1rem;
  }

  .learn-content-wrapper {
    padding: 0 15px;
  }

  .learn-content-box {
    padding: 30px 25px;
  }

  .learn-star {
    left: -15px;
    top: -10px;
  }

  .star-decoration {
    width: 40px;
    height: 40px;
  }

  .learn-list li {
    font-size: 1rem;
    margin-bottom: 15px;
  }

  /* Navbar Mobile Responsive */
  .navbar {
    justify-content: space-between;
  }

  .navbar-brand {
    position: static;
  }

  .navbar-container {
    display: none;
  }

  .hamburger-menu {
    display: flex;
    position: static;
  }

  .mobile-nav-dropdown {
    left: 20px;
    right: 20px;
    transform: none;
    min-width: auto;
  }

  .tools-grid {
    flex-direction: column;
    gap: 20px;
    padding: 0 15px;
  }

  .tool-card {
    flex: 1 1 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  .tools-header {
    margin-bottom: 50px;
  }

  .tools-header h2 {
    font-size: 2.2rem;
  }

  .tools-message-content {
    padding: 10px 20px;
    font-size: 0.85rem;
  }

  .star-icon-svg {
    width: 30px;
    height: 30px;
  }

  .star-top-left {
    top: 60px;
    left: 5%;
  }

  .star-bottom-right {
    bottom: 60px;
    right: 5%;
  }
}

/* Everything You'll Receive Section */
.receive-section {
  background-color: white;
  color: #333;
  padding: 80px 0;
  position: relative;
}

.receive-title {
  text-align: center;
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 60px;
  color: #333;
  line-height: 1.2;
}

.price-highlight {
  color: #7ED321;
  font-weight: 900;
}

.receive-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.receive-card {
  border-radius: 20px;
  padding: 25px;
  position: relative;
  overflow: hidden;
  min-height: 210px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  flex: 0 0 calc(33.333% - 17px);
  max-width: calc(33.333% - 17px);
}

.receive-card:hover {
  transform: translateY(-5px);
}

/* Card Background Colors */
.receive-card.certificate {
  background: linear-gradient(135deg, #FFF4E6, #FFE4B5);
}

.receive-card.roadmap {
  background: linear-gradient(135deg, #FFE4E1, #FFC0CB);
}

.receive-card.portfolio {
  background: linear-gradient(135deg, #E6F3FF, #B3D9FF);
}

.receive-card.resource {
  background: linear-gradient(135deg, #F0E6FF, #D9B3FF);
  flex: 0 0 calc(50% - 12.5px);
  /* max-width: calc(50%); */
  margin-left: calc(16.666% + 8px);
}

.receive-card.whatsapp {
  background: linear-gradient(135deg, #E6FFF0, #B3FFD1);
  flex: 0 0 calc(50% - 12.5px);
  /* max-width: calc(50%); */
  /* margin-right: calc(16.666% + 8px); */
}

.card-content h3 {
  font-size: 1.3rem;
  font-weight: 400;
  margin-bottom: 12px;
  color: #333;
  line-height: 1.3;
  max-width: 74%;
}

.card-content p {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.4;
  max-width: 57%;
}

.card-icon {
  position: absolute;
  bottom: 15px;
  right: 15px;
  width: 70px;
  height: 70px;
}

.card-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

@media (max-width: 768px) {

  .hero-section .container {
    max-width: 750px !important;
    padding: 0 30px;
  }
.intro{
  max-width: 97%;
}
  .receive-grid {
    flex-direction: column;
    gap: 20px;
    padding: 0 15px;
  }

  .receive-card {
    flex: 1 1 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  .receive-card.resource,
  .receive-card.whatsapp {
    flex: 1 1 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  .receive-title {
    font-size: 2.5rem;
  }

  .card-content h3 {
    font-size: 1.2rem;
  }

  .card-content p {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .receive-title {
    font-size: 2rem;
  }

  .receive-card {
    min-height: 150px;
    padding: 20px;
  }
}

/* Meet Your Mentor Section */
.mentor-section {
  padding: 100px 0;
  background-color: #f8f9fa;
  text-align: center;
}

.mentor-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.mentor-header {
  margin-bottom: 50px;
}

.mentor-header h2 {
  font-size: 3.5rem;
  font-weight: 500;
  color: #000;
  margin: 0;
  line-height: 1.2;
}

.mentor-highlight {
      background-color: #9DFF50;
    padding: 4px 14px;
    display: inline-block;
    font-size: 0.85em;
  font-weight: 500;
  clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 8px 100%);
  position: relative;
}

.mentor-intro {
 margin-bottom: 20px;
}

.mentor-greeting {
  font-size: 1.8rem;
  color: #333;
  line-height: 1.6;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
}

.mentor-avatar-inline {
  width: 40px;
  height: 40px;
  object-fit: cover;
  margin: 0 5px;
}

.coffee-icon {
  width: 30px;
  height: 30px;
  object-fit: contain;
  margin: 0 5px;
}

.year-badge {
padding: 2px 18px;
    border-radius: 20px;
    border: 1px solid #bfbdbd;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 5px;
}

.mentor-description {
  max-width: 700px;
  margin: 0 auto;
}

.mentor-description p {
  font-size: 1.3rem;
    color: #555;
    line-height: 1.7;
    margin: 0;
}

.mentor-description strong {
  color: #000;
  font-weight: 700;
}

/* Mentor Section Tablet */
@media (max-width: 1024px) {
  .mentor-section {
    padding: 80px 0;
  }

  .mentor-content {
    padding: 0 30px;
  }

  .mentor-header h2 {
    font-size: 3rem;
  }

  .mentor-greeting {
    font-size: 1.6rem;
  }

  .mentor-description p {
    font-size: 1.1rem;
  }
}

/* Mentor Section Mobile */
@media (max-width: 768px) {
  .mentor-section {
    padding: 60px 0;
  }

  .mentor-content {
    padding: 0 15px;
  }

  .mentor-header h2 {
    font-size: 2.5rem;
  }

  .mentor-greeting {
    font-size: 1.4rem;
    flex-direction: column;
    gap: 15px;
  }

  .mentor-description p {
    font-size: 1.1rem;
  }

  .mentor-avatar-inline {
    width: 35px;
    height: 35px;
  }

  .coffee-icon {
    width: 25px;
    height: 25px;
  }
}

/* Testimonials Section */
.testimonials-section {
  background-color: #2c2c2c;
  color: white;
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.testimonials-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom right, white 49%, #2c2c2c 51%);
  z-index: 1;
}

.testimonials-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom right, #2c2c2c 49%, white 51%);
  z-index: 1;
}

.testimonials-section .container {
  position: relative;
  z-index: 2;
}

.testimonials-sidebar {
      position: absolute;
    left: -126px;
    top: 14%;
    transform: translateY(-50%) rotate(-90deg);
    transform-origin: center;
    z-index: 3;
    background: #333333;
    padding: 5px 15px;
}

.testimonials-label {
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 3px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
}

.star-decoration {
  position: absolute;
  z-index: 2;
  left: 69px;
}

.star-decoration .star-icon-svg {
  width: 40px;
  height: 40px;
  opacity: 0.3;
}

.star-top {
  top: 15%;
  right: 10%;
}

.star-bottom {
  bottom: 15%;
  left: 8%;
}

.testimonials-content {
  margin-left: 60px;
  text-align: left;
}

.testimonials-header {
  margin-bottom: 60px;
  text-align: left;
}

.testimonials-header h2 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: white;
}

.testimonials-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

/* Custom Testimonials Carousel */
.testimonials-carousel {
  position: relative;
}

.testimonials-slide {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 40px;
  padding: 0 20px;
  min-height: 400px;
  position: relative;
}

.testimonial-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 35px;
  width: 380px;
  flex-shrink: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
}

/* Position-based styling */
.testimonial-card[data-position="left"] {
  left: calc(50% - 600px);
  margin-top: 40px;
  opacity: 0.6;
  transform: scale(0.95);
  z-index: 1;
}

.testimonial-card[data-position="center"] {
  left: 50%;
  transform: translateX(-50%) scale(1.0);
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 2;
  opacity: 1;
  margin-top: 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.testimonial-card[data-position="right"] {
  right: calc(50% - 600px);
  margin-top: 40px;
  opacity: 0.6;
  transform: scale(0.95);
  z-index: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.author-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: white;
}

.author-info p {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.testimonial-text {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-style: italic;
}

/* Navigation at Bottom */
.testimonials-navigation {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 50px;
}

.testimonial-nav-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: transparent;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.testimonial-nav-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Testimonials Tablet */
@media (max-width: 1024px) {
  .testimonials-content {
    margin-left: 40px;
  }

  .testimonials-header h2 {
    font-size: 2.8rem;
  }

  .testimonials-slide {
    gap: 30px;
  }

  .testimonial-card {
    width: 320px;
    padding: 30px;
  }

  .testimonial-card[data-position="left"] {
    left: calc(50% - 500px);
  }

  .testimonial-card[data-position="right"] {
    right: calc(50% - 500px);
  }

  .star-decoration .star-icon-svg {
    width: 35px;
    height: 35px;
  }
}

/* Testimonials Mobile */
@media (max-width: 768px) {
  .testimonials-content {
    margin-left: 0;
  }

  .testimonials-header {
    text-align: center;
  }

  .testimonials-header h2 {
    font-size: 2.2rem;
  }

  .testimonials-slide {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    min-height: auto;
  }

  .testimonial-card {
    position: relative !important;
    left: auto !important;
    right: auto !important;
    transform: none !important;
    width: 100%;
    max-width: 350px;
    margin: 0 !important;
    opacity: 1 !important;
  }

  .testimonial-card[data-position="left"],
  .testimonial-card[data-position="right"],
  .testimonial-card[data-position="center"] {
    position: relative;
    left: auto;
    right: auto;
    transform: none;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    margin-top: 0 !important;
  }

  .star-decoration .star-icon-svg {
    width: 30px;
    height: 30px;
  }

  .testimonials-sidebar {
    display: none;
  }
}

/* FAQ Section */
.faq-section {
  padding: 100px 0;
  background-color: #f8f9fa;
  position: relative;
}

.faq-header {
  text-align: center;
  margin-bottom: 80px;
}

.decorative-title {
  font-size: 6rem;
  font-weight: 900;
  color: #000;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}



.star-separator {
  width: 32px;
  height: 32px;
  object-fit: contain;
  margin: 0 10px;
}

.title-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  width: 100%;
  max-width: 970px;
  margin-left: auto;
  margin-right: auto;
}

.divider-line {
  flex: 1;
  height: 2px;
  background-color: #000;
}

.divider-star {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.faq-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  align-items: flex-start;
}

.faq-left {
  padding-right: 40px;
}

.faq-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: #FFFFFF;
  color: black;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 20px;
  border: 1px solid #B6BCCD;
}

.faq-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.faq-left h3 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #000;
  margin-bottom: 20px;
  line-height: 1.2;
}

.faq-left p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
}

.faq-right {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Bootstrap Accordion Customization */
.accordion {
  --bs-accordion-border-width: 0;
  --bs-accordion-border-radius: 0;
  --bs-accordion-bg: transparent;
}

.accordion-item {
  border-bottom: 1px solid #e5e7eb;
  background: transparent;
  border: none;
  border-radius: 0;
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-button {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 25px 0;
  background: transparent;
  border: none;
  box-shadow: none;
  color: inherit;
  font-size: inherit;
  transition: all 0.3s ease;
}

.accordion-button:not(.collapsed) {
  background: transparent;
  color: #7A86F2;
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: none;
  border: none;
}

.accordion-button::after {
  content: '';
   background-image: url('../asserts/up.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 16px;
  height: 16px;
  margin-left: auto;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.accordion-button:not(.collapsed)::after {
   background-image: url('../asserts/down.svg');
  transform: none;
}

.accordion-button:hover {
  color: #7A86F2;
}

.question-number {
  color: #7A86F2;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  font-weight: 600;
  flex-shrink: 0;
}

.question-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #000;
  flex-grow: 1;
}



.accordion-collapse {
  border: none;
}

.accordion-body {
  padding: 0 0 15px 39px;
  background: transparent;
}

.faq-answer p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* FAQ Tablet */
@media (max-width: 1024px) {
  .faq-section {
    padding: 80px 0;
  }

  .faq-container {
    padding: 0 30px;
  }

  .decorative-title {
    font-size: 3rem;
  }

  .faq-left h3 {
    font-size: 2.2rem;
  }
}

/* FAQ Mobile */
@media (max-width: 768px) {
  .faq-section {
    padding: 60px 0;
  }

  .faq-container {
    padding: 0 15px;
  }

  .decorative-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: 15px;
  }

  .faq-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .faq-left {
    padding-right: 0;
    text-align: center;
  }

  .divider-line {
    width: 100px;
  }

  .faq-left h3 {
    font-size: 2rem;
  }

  .accordion-button {
    padding: 20px 0;
    font-size: 0.95rem;
  }

  .faq-answer p {
    font-size: 0.9rem;
  }
}

/* Footer Section */
.footer-section {
  background-color: #222222;
  color: white;
  padding: 80px 0 40px;
  text-align: center;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  margin-bottom: 60px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.footer-logo-img {
  align-self: baseline;
    margin-top: 27px;
    margin-right: 21px;
}

.footer-brand-text h3 {
      font-size: 7rem;
  font-weight: 300;
  margin: 0;
  color: white;
  letter-spacing: -1px;
}

.footer-brand-text h3 span.design {
  color: #7ED321;
  font-weight: 300;
}

.footer-brand-text p {
 font-size: 2rem;
    margin: 10px 0 0 0;
    color: white;
    font-weight: 700;
    letter-spacing: 1px;
}

/* Footer Bottom Section - Horizontal Layout */
.footer-copyright {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 40px 10px 40px;
}

.footer-copyright p {
  margin: 0;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 300;
}

.footer-social {
  display: flex;
  gap: 20px;
  align-items: center;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: white;
  font-size: 1rem;
}

.social-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  color: white;
}

/* Footer Tablet */
@media (max-width: 1024px) {
  .footer-section {
    padding: 60px 0 30px;
  }

  .footer-brand-text h3 {
    font-size: 3rem;
  }

  .footer-brand-text p {
    font-size: 1.1rem;
  }
}

/* Footer Mobile */
@media (max-width: 768px) {
  .footer-section {
    padding: 50px 0 25px;
  }

  .footer-content {
    gap: 30px;
    margin-bottom: 40px;
  }

  .footer-brand-text h3 {
    font-size: 2.5rem;
  }

  .footer-brand-text p {
    font-size: 1rem;
  }

  .footer-copyright {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .footer-social {
    gap: 20px;
    justify-content: center;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .footer-section {
    padding: 40px 0 20px;
  }

  .footer-brand-text h3 {
    font-size: 2rem;
  }

  .footer-brand-text p {
    font-size: 0.9rem;
  }

  .footer-copyright p {
    font-size: 0.9rem;
  }

  .footer-social {
    gap: 15px;
  }

  .social-link {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .intro {
    margin: 5px;
    border-radius: 15px;
    padding: 15px 10px;
    min-height: 400px;
  }

  h1 {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1.3rem;
  }

  .description {
    font-size: 0.9rem;
  }

  .btn {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }

  .btn-register-final {
    padding: 1rem 2.2rem;
    font-size: 1rem;
  }

  .masterclass-badge-new {
    font-size: 0.8rem;
    padding: 10px 20px;
    border-radius: 40px;
  }

  .event-item {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }

  .event-icon {
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
  }



  /* Modal Mobile Styles - Mobile First Approach */

  /* Extra Small Mobile (320px - 480px) */
  @media (max-width: 480px) {
    .modal-overlay {
      padding: 5px;
      align-items: flex-start;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .modal-container {
      width: 100%;
      max-width: 100%;
      margin: 10px 0;
      padding: 15px;
      max-height: none;
      min-height: auto;
      border-radius: 12px;
      overflow-y: visible;
    }

    .modal-header {
      padding: 10px 0 15px 0;
    }

    .modal-header p {
      font-size: 16px;
      margin-bottom: 12px;
    }

    .progress-steps {
      gap: 6px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .step {
      flex: none;
    }

    .step-icon {
      width: 28px;
      height: 28px;
      font-size: 0.9rem;
    }

    .step-label {
      font-size: 0.55rem;
    }

    .step-line {
      width: 15px;
      height: 2px;
    }

    /* Stack layout for mobile */
    .modal-content {
      flex-direction: column !important;
      gap: 15px;
      overflow-y: visible;
      max-height: none;
    }

    .calendar-section {
      width: 100% !important;
      margin-bottom: 15px;
      padding: 15px;
      flex-shrink: 0;
    }

    .time-section {
      width: 100% !important;
      padding: 15px;
      flex-shrink: 0;
    }

    .time-slots-container {
      max-height: none !important;
      overflow-y: visible !important;
    }

    .time-slots-grid {
      grid-template-columns: repeat(2, 1fr) !important;
      gap: 6px;
      max-height: none;
    }

    .time-slot {
      padding: 6px 8px;
      font-size: 0.75rem;
      min-height: 32px;
    }

    /* Form adjustments for mobile */
    .modal-container.form-step {
      padding: 15px;
      min-height: auto;
      max-height: none;
      overflow-y: visible;
    }

    .info-content {
      overflow-y: visible !important;
      max-height: none !important;
    }

    .registration-form {
      gap: 12px;
    }

    .form-group input,
    .form-group select {
      padding: 10px;
      font-size: 14px;
    }

    .form-actions {
      flex-direction: column;
      gap: 10px;
      margin-top: 20px;
    }

    .btn {
      width: 100%;
      padding: 12px;
      font-size: 12px;
    }

    /* Success screen mobile */
    .success-content {
      padding: 15px 20px;
      gap: 20px;
      max-height: none !important;
      overflow-y: visible !important;
    }

    .success-actions {
      flex-direction: column;
      gap: 10px;
      width: 100%;
    }

    .calendar-btn,
    .whatsapp-btn,
    .done-btn {
      width: 100%;
      padding: 12px;
      font-size: 14px;
    }
  }

  /* Small Mobile to Tablet (481px - 768px) */
  @media (min-width: 481px) and (max-width: 768px) {
    .modal-overlay {
      padding: 15px;
    }

    .modal-container {
      width: 90%;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      max-height: 90vh;
    }

    .modal-header p {
      font-size: 18px;
    }

    .progress-steps {
      gap: 10px;
    }

    .step-icon {
      width: 35px;
      height: 35px;
      font-size: 1.1rem;
    }

    .step-label {
      font-size: 0.65rem;
    }

    .step-line {
      width: 35px;
    }

    /* Keep side-by-side for tablets */
    .modal-content {
      flex-direction: row;
      gap: 20px;
    }

    .calendar-section {
      flex: 1;
      min-width: 250px;
      padding: 20px;
    }

    .time-section {
      flex: 1;
      min-width: 250px;
      padding: 20px;
    }

    .time-slots-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }

    .time-slot {
      padding: 8px 10px;
      font-size: 0.85rem;
    }

    /* Form adjustments for tablet */
    .modal-container.form-step {
      max-width: 700px;
      padding: 25px;
    }

    .registration-form {
      gap: 15px;
    }

    .form-actions {
      flex-direction: row;
      justify-content: space-between;
    }

    .btn {
      padding: 12px 24px;
    }
  }

  /* Laptop/Small Desktop (769px - 1024px) */
  @media (min-width: 769px) and (max-width: 1024px) {
    .modal-container {
      width: 80%;
      max-width: 800px;
      padding: 25px 30px;
    }

    .modal-header p {
      font-size: 20px;
    }

    .progress-steps {
      gap: 15px;
    }

    .step-icon {
      width: 40px;
      height: 40px;
      font-size: 1.2rem;
    }

    .step-line {
      width: 60px;
    }

    .modal-content {
      gap: 25px;
    }

    .time-slots-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
    }

    .modal-container.form-step {
      max-width: 850px;
      padding: 30px 35px;
    }
  }

  /* Large Desktop (1025px+) */
  @media (min-width: 1025px) {
    .modal-container {
      width: 70%;
      max-width: 900px;
      padding: 30px 40px;
    }

    .progress-steps {
      gap: 20px;
    }

    .step-icon {
      width: 50px;
      height: 50px;
      font-size: 1.4rem;
    }

    .step-line {
      width: 80px;
    }

    .modal-content {
      gap: 30px;
    }

    .time-slots-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
    }

    .modal-container.form-step {
      max-width: 900px;
      padding: 30px 40px;
    }
  }

  .learn-section {
    padding: 40px 0;
  }

  .learn-title {
    font-size: 1.8rem;
  }

  .learn-content-box {
    padding: 25px 20px;
  }

  .learn-star {
    left: -10px;
    top: -8px;
  }

  .star-decoration {
    width: 35px;
    height: 35px;
  }

  .tools-header h2 {
    font-size: 1.8rem;
  }

  .tools-message-content {
    padding: 8px 15px;
    font-size: 0.8rem;
  }

  .star-icon-svg {
    width: 25px;
    height: 25px;
  }

  .ai-tools-section {
    padding: 60px 0;
  }

  .mentor-header h2 {
    font-size: 2rem;
  }

  .mentor-greeting {
    font-size: 1.2rem;
  }

  .mentor-description p {
    font-size: 1rem;
  }

  .testimonials-header h2 {
    font-size: 1.8rem;
  }

  .testimonial-card {
    padding: 25px;
    max-width: 300px;
  }

  .star-decoration .star-icon-svg {
    width: 25px;
    height: 25px;
  }

  .decorative-title {
    font-size: 2rem;
  }

  .faq-left h3 {
    font-size: 1.8rem;
  }
}

/* Info Content Styles */
.info-content {
  background: white;
  width: 100%;
  height: 650px;
  /* padding: 20px 0; */
  overflow-y: visible;
  /* margin-top: 20px; */
}

/* Adjust info content for form step modal */
.modal-container.form-step .info-content {
  height: auto;
  min-height: 500px;
  max-height: none;
  overflow-y: auto;
}

.progress-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  gap: 30px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  background-color: #f0f0f0;
  color: #999;
  border: 2px solid #f0f0f0;
}

.step.completed .step-icon {
  background-color: #9DFF50;
  color: #000;
  border-color: #9DFF50;
}

.step.active .step-icon {
  background-color: #9DFF50;
  color: #000;
  border-color: #9DFF50;
}

/* Override existing step styles for info form */
.step.completed .step-icon .clock-icon,
.step.completed .step-icon .info-icon,
.step.completed .step-icon .success-icon {
  display: none;
}

.step.completed .step-icon::after {
  content: '✓';
  font-size: 16px;
  font-weight: bold;
}

.step.active .step-icon .info-icon {
  display: block;
}

.step.active .step-icon::after {
  content: '📝';
  font-size: 16px;
}

.step span {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.step.completed span,
.step.active span {
  color: #9DFF50;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 60px;
  width: 40px;
  height: 2px;
  background-color: #f0f0f0;
}

.step.completed:not(:last-child)::after {
  background-color: #9DFF50;
}

.modal-title {
  text-align: center;
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 25px;
}

.selected-time-display {
  background-color: #f0fff4;
  border: 2px solid #9DFF50;
  border-radius: 12px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
  font-weight: 600;
  color: #333;
}

.selected-time-display .calendar-icon,
.selected-time-display .time-icon {
  font-size: 18px;
}

.registration-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  max-width: 100%;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-group label .icon {
  font-size: 16px;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background-color: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #9DFF50;
}

.form-group input::placeholder {
  color: #999;
}

.checkbox-group {
  flex-direction: row !important;
  align-items: flex-start;
  gap: 12px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
  background-color: #9DFF50;
  border-color: #9DFF50;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
  content: '✓';
  color: #000;
  font-weight: bold;
  font-size: 12px;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.back-btn {
  flex: 1;
  padding: 15px 20px;
  border: 2px solid #e0e0e0;
  background-color: white;
  color: #666;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  border-color: #ccc;
  background-color: #f9f9f9;
}

.submit-btn {
  flex: 2;
  padding: 15px 20px;
  background-color: #9DFF50;
  color: #000;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: #8AE63B;
  transform: translateY(-1px);
}

/* Success Screen Styles */
.success-content {
  background: white;
  width: 100%;
  padding: 20px 0;
  overflow-y: auto;
  text-align: center;
}

.success-header {
  margin-bottom: 30px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: #9DFF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.success-icon .checkmark {
  font-size: 40px;
  color: #000;
  font-weight: bold;
}

.success-title {
  font-size: 28px;
  font-weight: 700;
  color: #22c55e;
  margin: 0 0 15px 0;
}

.success-message {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin: 0;
  max-width: 500px;
  margin: 0 auto;
}

.masterclass-details-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #1a1a2e;
  color: white;
  border-radius: 12px;
  padding: 25px;
  margin: 30px 0;
  text-align: left;
}

.masterclass-details-card h3 {
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  font-size: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  font-size: 18px;
  color: #9DFF50;
}

.next-steps-card {
  background-color: #f0fff4;
  border: 2px solid #9DFF50;
  border-radius: 12px;
  padding: 25px;
  margin: 30px 0;
  text-align: left;
}

.next-steps-card h3 {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 24px;
  height: 24px;
  background-color: #9DFF50;
  color: #000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 2px;
}

.step-content {
  flex: 1;
}

.step-content strong {
  display: block;
  color: #333;
  font-size: 16px;
  margin-bottom: 5px;
}

.step-content p {
  color: #666;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

.success-actions {
  display: flex;
  gap: 12px;
  margin: 25px auto;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  width: fit-content;
  max-width: 500px;
  align-self: center;
}

.calendar-btn,
.whatsapp-btn {
  flex: 1;
  min-width: 160px;
  max-width: 200px;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  background-color: white;
  color: #333;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.calendar-btn:hover,
.whatsapp-btn:hover {
  border-color: #9DFF50;
  background-color: #f0fff4;
  transform: translateY(-1px);
}

.done-btn {
  flex: 0 0 auto;
  min-width: 80px;
  padding: 12px 20px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.done-btn:hover {
  background-color: #555;
  transform: translateY(-1px);
}

.help-text {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  text-align: center;
}

.help-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.help-text strong {
  color: #333;
}

/* Additional Mobile Optimizations */
@media (max-width: 480px) {

  /* Ensure modal doesn't overflow on very small screens */
  .modal-overlay {
    align-items: flex-start;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  /* Fix body scrolling on mobile */
  body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  /* Better touch targets for mobile */
  .time-slot {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Improve form usability on mobile */
  .form-group label {
    font-size: 14px;
    margin-bottom: 6px;
  }

  /* Better close button for mobile */
  .close-btn {
    width: 32px;
    height: 32px;
    font-size: 18px;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  /* Adjust calendar for mobile */
  .calendar-dates {
    gap: 4px;
  }

  .calendar-dates span {
    padding: 8px 4px;
    font-size: 0.75rem;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Ensure all modal sections are scrollable */
  .modal-header,
  .modal-content,
  .info-content,
  .success-content {
    flex-shrink: 0;
  }
}

/* Tablet specific optimizations */
@media (min-width: 481px) and (max-width: 768px) {

  /* Better spacing for tablet */
  .time-slots-container {
    max-height: 300px;
  }

  /* Optimize form layout for tablet */
  .registration-form .form-row {
    display: flex;
    gap: 15px;
  }

  .registration-form .form-row .form-group {
    flex: 1;
  }
}

/* Responsive adjustments for success actions */
@media (max-width: 600px) {
  .success-actions {
    flex-direction: column;
    gap: 10px;
  }

  .calendar-btn,
  .whatsapp-btn,
  .done-btn {
    flex: none;
    width: 100%;
    max-width: none;
  }
}

/* Footer Section */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #2c2c2c;
  padding: 12px 20px;
  z-index: 1000;
  border-top: 1px solid #404040;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}



.footer-nav {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-nav-item {
  color: #ccc;
  text-decoration: none;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.3s ease;
}

.footer-nav-item:hover {
  color: #fff;
}

.footer-nav-item.with-icon::before {
  font-size: 14px;
  color: #9DFF50;
}

.footer-nav-item.calendar::before {
  content: "�️";
}

.footer-nav-item.time::before {
  content: "🕐";
}

.footer-nav-item.zoom::before {
  content: "�";
}

.footer-nav-item.certificate::before {
  content: "�";
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-btn {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.footer-btn.register {
  background-color: #ff4757;
  color: white;
}

.footer-btn.register:hover {
  background-color: #ff3742;
  transform: translateY(-1px);
}

.footer-btn.start {
  background-color: #9DFF50;
  color: #333;
}

.footer-btn.start:hover {
  background-color: #8ae63d;
  transform: translateY(-1px);
}

.footer-user {
  color: #ccc;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.footer-user::before {
  content: "👤";
  font-size: 12px;
}



/* Responsive footer */
@media (max-width: 768px) {
  .footer {
    padding: 10px 15px;
  }

  .footer-content {
    flex-direction: column;
    gap: 15px;
  }

  .footer-left {
    flex-direction: column;
    gap: 10px;
  }

  .footer-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  .footer-right {
    gap: 8px;
  }

  .footer-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* Success content styling and scrolling */
.success-content {
  padding: 20px 30px;
  max-height: 600px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 25px;
  align-items: center;
  text-align: center;
}

/* Adjust success content for form step modal */
.modal-container.form-step .success-content {
  flex: 1;
  height: auto;
  min-height: 500px;
  max-height: calc(95vh - 200px);
  overflow-y: auto;
  padding: 20px 0;
}

/* Custom scrollbar for success content */
.success-content::-webkit-scrollbar {
  width: 6px;
}

.success-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.success-content::-webkit-scrollbar-thumb {
  background: #9DFF50;
  border-radius: 3px;
}

.success-content::-webkit-scrollbar-thumb:hover {
  background: #8ae63d;
}

/* Success header styling */
.success-header {
  text-align: center;
  margin-bottom: 20px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: #9DFF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.success-icon .checkmark {
  font-size: 40px;
  color: #333;
  font-weight: bold;
}

.success-title {
  font-size: 28px;
  font-weight: 700;
  color: #9DFF50;
  margin: 0 0 15px 0;
}

.success-message {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Masterclass details card */
.masterclass-details-card {
  background-color: #2c3e50;
  color: white;
  padding: 25px;
  border-radius: 12px;
  margin: 20px auto;
  width: 100%;
  max-width: 500px;
  box-sizing: border-box;
  align-self: center;
}

.masterclass-details-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
  color: white;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  font-size: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

/* Next steps card */
.next-steps-card {
  background-color: #f0fff4;
  border: 2px solid #9DFF50;
  border-radius: 12px;
  padding: 25px;
  margin: 20px auto;
  width: 100%;
  max-width: 500px;
  box-sizing: border-box;
  text-align: left;
  align-self: center;
}

.next-steps-card h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
  color: #333;
}

.step-item {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  background-color: #9DFF50;
  color: #333;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content strong {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  font-size: 15px;
}



.calendar-btn,
.whatsapp-btn {
  flex: 1;
  min-width: 180px;
  padding: 12px 20px;
  border: 2px solid #e0e0e0;
  background-color: white;
  color: #333;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.calendar-btn:hover,
.whatsapp-btn:hover {
  border-color: #9DFF50;
  background-color: #f0fff4;
}

.done-btn {
  flex: 0 0 100px;
  padding: 12px 20px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.done-btn:hover {
  background-color: #555;
}

.help-text {
  background-color: #f0fff4;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.help-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.help-text strong {
  color: #333;
}


.strikethrough{
  text-decoration: line-through;
  padding-left: 4px;
}

@media screen and (max-width: 576px) {

  .masterclass-badge-new {
    display: inline-block;
    font-size: 12px;
    padding: 4px 19px;
  }
}


@media screen and (max-width: 460px) {

  h1.display-4{
   font-size: 26px !important;
  }
}